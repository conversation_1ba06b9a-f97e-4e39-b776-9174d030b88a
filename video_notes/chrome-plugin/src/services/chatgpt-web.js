// 100% 复制自 chatGPTBox/src/services/apis/chatgpt-web.mjs

import { fetchSSE } from '../utils/fetch-sse.js'
import { isEmpty } from 'lodash-es'
import { getUserConfig, ChatGPTModels } from '../config/index.js'
import { pushRecord, setAbortController } from './shared.js'
import { v4 as uuidv4 } from 'uuid'
import { sha3_512 } from 'js-sha3'
import randomInt from 'random-int'
import { getModelValue } from '../utils/model-name-convert.js'

// 模拟翻译函数
const t = (s) => s

// 模拟Browser API
const Browser = {
  cookies: {
    getAll: (details) => new Promise((resolve) => {
      chrome.cookies.getAll(details, resolve)
    }),
    get: (details) => new Promise((resolve) => {
      chrome.cookies.get(details, resolve)
    })
  }
}

async function request(token, method, path, data) {
  const apiUrl = (await getUserConfig()).customChatGptWebApiUrl
  const requestBody = JSON.stringify(data);
  const response = await fetch(`${apiUrl}/backend-api${path}`, {
    method,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: requestBody,
  })
  const responseText = await response.text()
  return { response, responseText }
}

export async function sendMessageFeedback(token, data) {
  await request(token, 'POST', '/conversation/message_feedback', data)
}

export async function setConversationProperty(token, conversationId, propertyObject) {
  await request(token, 'PATCH', `/conversation/${conversationId}`, propertyObject)
}

export async function deleteConversation(token, conversationId) {
  await request(token, 'PATCH', `/conversation/${conversationId}`, { is_visible: false })
}

export async function sendModerations(token, question, conversationId, messageId) {
  await request(token, 'POST', `/moderations`, {
    conversation_id: conversationId,
    input: question,
    message_id: messageId,
    model: 'text-moderation-playground',
  })
}

export async function getModels(accessToken) {
  const response = JSON.parse((await request(accessToken, 'GET', '/models')).responseText)
  if (response) {
    return response.models.map((m) => m.slug)
  }
}

export async function isNeedWebsocket(accessToken) {
  const response = JSON.parse((await request(accessToken, 'GET', '/accounts/check')).responseText)
  if (response) {
    return response.account_plan?.is_paid_subscription_active
  }
}

export async function getRequirements(accessToken) {
  const response = JSON.parse(
    (await request(accessToken, 'POST', '/sentinel/chat-requirements')).responseText,
  )
  if (response) {
    return response
  }
}

export async function getArkoseToken(config) {
  if (!config.chatgptArkoseReqUrl)
    throw new Error(
      t('Please login at https://chatgpt.com first') +
      '\n\n' +
      t(
        "Please keep https://chatgpt.com open and try again. If it still doesn't work, type some characters in the input box of chatgpt web page and try again.",
      ),
    )
  const arkoseToken = await fetch(
    config.chatgptArkoseReqUrl + '?' + config.chatgptArkoseReqParams,
    {
      method: 'POST',
      body: config.chatgptArkoseReqForm,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      },
    },
  )
    .then((resp) => resp.json())
    .then((resp) => resp.token)
    .catch(() => null)
  if (!arkoseToken)
    throw new Error(
      t('Failed to get arkose token.') +
      '\n\n' +
      t(
        "Please keep https://chatgpt.com open and try again. If it still doesn't work, type some characters in the input box of chatgpt web page and try again.",
      ),
    )
  return arkoseToken
}

// https://github.com/tctien342/chatgpt-proxy/blob/9147a4345b34eece20681f257fd475a8a2c81171/src/openai.ts#L103
// https://github.com/zatxm/aiproxy
function generateProofToken(seed, diff, userAgent) {
  const cores = [1, 2, 4]
  const screens = [3008, 4010, 6000]
  const reacts = [
    '_reactListeningcfilawjnerp',
    '_reactListening9ne2dfo1i47',
    '_reactListening410nzwhan2a',
  ]

  const parseTime = new Date().toString()
  const config = [
    cores[randomInt(0, cores.length - 1)],
    screens[randomInt(0, screens.length - 1)],
    parseTime,
    4294705152,
    0,
    userAgent,
    'https://chatgpt.com',
    'dpl=1440c5cdf6f7665006d8bf0efbfdc695',
    'en-US',
    'en-US,en;q=0.9',
    0,
  ]

  const diffLen = diff.length / 2
  for (let i = 0; i < 100000; i++) {
    config[4] = i
    config[10] = reacts[randomInt(0, reacts.length - 1)]
    const jsonData = JSON.stringify(config)
    const base = stringToBase64(jsonData)
    const hashValue = sha3_512(seed + base)

    if (hashValue.substring(0, diffLen) <= diff) {
      const result = 'gAAAAAB' + base
      return result
    }
  }

  const fallbackBase = stringToBase64(JSON.stringify(config))
  return 'gAAAAABwQ8Lk5FbGpA2NcR9dShT6gYjU7VxZ4D' + fallbackBase.substring(0, 8)
}

function stringToBase64(str) {
  return btoa(unescape(encodeURIComponent(str)))
}

/**
 * 预处理问题内容
 * @param {string} question - 原始问题
 * @returns {Promise<Object>} 处理后的问题信息
 */
async function preprocessQuestion(question) {
  if (typeof question !== 'string') {
    return { processedQuestion: question, truncated: false, percent: 0 };
  }

  // 获取用户配置
  const config = await getUserConfig();

  // 根据模型动态确定最大token限制
  let maxTokens;
  const modelName = getModelValue({ modelName: config.modelName });

  // 根据模型设置合适的token限制
  if (modelName.includes('gpt-4')) {
    maxTokens = 8192; // GPT-4 标准上下文窗口
  } else if (modelName.includes('gpt-4o')) {
    maxTokens = 128000; // GPT-4o 上下文窗口
  } else {
    // GPT-3.5 或其他模型
    maxTokens = 16384;
  }

  // 考虑历史记录和其他内容的token消耗，预留一些空间
  const safeMaxTokens = Math.floor(maxTokens * 0.8);

  // 估算字符到token的转换比例
  // 英文大约是4个字符/token，中文大约是1-2个字符/token
  const CHARS_PER_TOKEN = 3; // 使用中间值作为估计
  const MAX_CHARS = safeMaxTokens * CHARS_PER_TOKEN;

  let truncated = false;
  let origQuestion = question;
  let percent = 0;

  if (question.length > MAX_CHARS) {
    question = question.slice(0, MAX_CHARS) +
      `\n\n【内容已截断，超出模型 ${modelName} 的最大支持长度】`;
    truncated = true;
    percent = Math.round((1 - MAX_CHARS / origQuestion.length) * 100);
  }

  return {
    processedQuestion: question,
    truncated,
    percent
  };
}

export async function sendWebsocketConversation(accessToken, options) {
  const apiUrl = (await getUserConfig()).customChatGptWebApiUrl
  const response = await fetch(`${apiUrl}/backend-api/conversation`, options).then((r) => r.json())
  console.debug(`request: ws /conversation`, response)
  return { conversationId: response.conversation_id, wsRequestId: response.websocket_request_id }
}

export async function stopWebsocketConversation(accessToken, conversationId, wsRequestId) {
  await request(accessToken, 'POST', '/stop_conversation', {
    conversation_id: conversationId,
    websocket_request_id: wsRequestId,
  })
}

/**
 * @type {WebSocket}
 */
let websocket
/**
 * @type {Date}
 */
let expires_at
let wsCallbacks = []

export async function registerWebsocket(accessToken) {
  if (websocket && new Date() < expires_at - 300000) return

  const response = JSON.parse(
    (await request(accessToken, 'POST', '/register-websocket')).responseText,
  )
  let resolve
  if (response.wss_url) {
    websocket = new WebSocket(response.wss_url)
    websocket.onopen = () => {
      console.debug('global websocket opened')
      resolve()
    }
    websocket.onclose = () => {
      websocket = null
      expires_at = null
      console.debug('global websocket closed')
    }
    websocket.onmessage = (event) => {
      wsCallbacks.forEach((cb) => cb(event))
    }
    expires_at = new Date(response.expires_at)
  }
  return new Promise((r) => (resolve = r))
}

/**
 * @param {Runtime.Port} port
 * @param {string} question
 * @param {Session} session
 * @param {string} accessToken
 */
export async function generateAnswersWithChatgptWebApi(port, question, session, accessToken) {
  // 预处理问题
  const { processedQuestion, truncated, percent } = await preprocessQuestion(question);
  question = processedQuestion;

  // 记录日志
  let logMsg = '';
  if (question.length > 200) {
    const head = question.slice(0, 100);
    const tail = question.slice(-100);
    logMsg = `[generateAnswersWithChatgptWebApi] question: ${head} ...省略... ${tail}` +
      (truncated ? ` (已截断${percent}%)` : '');
  } else {
    logMsg = `[generateAnswersWithChatgptWebApi] question: ${question}`;
  }
  console.debug(logMsg);
  const { controller, cleanController } = setAbortController(
    port,
    () => {
      if (session.wsRequestId)
        stopWebsocketConversation(accessToken, session.conversationId, session.wsRequestId)
    },
    () => {
      if (session.autoClean) deleteConversation(accessToken, session.conversationId)
    },
  )

  const config = await getUserConfig()
  let arkoseError
  const [models, requirements, arkoseToken, useWebsocket] = await Promise.all([
    getModels(accessToken).catch(() => undefined),
    getRequirements(accessToken).catch(() => undefined),
    getArkoseToken(config).catch((e) => {
      arkoseError = e
    }),
    isNeedWebsocket(accessToken).catch(() => undefined),
  ])
  console.debug('models', models)
  const selectedModel = getModelValue(session)
  const usedModel =
    models && models.includes(selectedModel) ? selectedModel : ChatGPTModels.chatgptFree35.value
  console.debug('usedModel', usedModel)
  const needArkoseToken = requirements && requirements.arkose?.required
  if (arkoseError && needArkoseToken) throw arkoseError

  let proofToken
  if (requirements?.proofofwork?.required) {
    proofToken = generateProofToken(
      requirements.proofofwork.seed,
      requirements.proofofwork.difficulty,
      navigator.userAgent,
    )
  }

  let cookie
  let oaiDeviceId
  if (Browser.cookies && Browser.cookies.getAll) {
    cookie = (await Browser.cookies.getAll({ url: 'https://chatgpt.com/' }))
      .map((cookie) => {
        return `${cookie.name}=${cookie.value}`
      })
      .join('; ')
    oaiDeviceId = (
      await Browser.cookies.get({
        url: 'https://chatgpt.com/',
        name: 'oai-did',
      })
    ).value
  }

  const url = `${config.customChatGptWebApiUrl}${config.customChatGptWebApiPath}`
  session.messageId = uuidv4()
  session.wsRequestId = uuidv4()
  if (session.parentMessageId == null) {
    session.parentMessageId = uuidv4()
  }
  const options = {
    method: 'POST',
    signal: controller.signal,
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessToken}`,
      ...(cookie && { Cookie: cookie }),
      ...(needArkoseToken && { 'Openai-Sentinel-Arkose-Token': arkoseToken }),
      ...(requirements && { 'Openai-Sentinel-Chat-Requirements-Token': requirements.token }),
      ...(proofToken && { 'Openai-Sentinel-Proof-Token': proofToken }),
      'Oai-Device-Id': oaiDeviceId,
      'Oai-Language': 'en-US',
    },
    body: JSON.stringify({
      action: 'next',
      conversation_id: session.conversationId || undefined,
      messages: [
        {
          id: session.messageId,
          author: {
            role: 'user',
          },
          content: {
            content_type: 'text',
            parts: [question],
          },
        },
      ],
      conversation_mode: {
        kind: 'primary_assistant',
      },
      force_paragen: false,
      force_rate_limit: false,
      suggestions: [],
      model: usedModel,
      parent_message_id: session.parentMessageId,
      timezone_offset_min: new Date().getTimezoneOffset(),
      history_and_training_disabled: config.disableWebModeHistory,
      websocket_request_id: session.wsRequestId,
    }),
  }

  let answer = ''
  let generationPrefixAnswer = ''
  let generatedImageUrl = ''

  if (useWebsocket) {
    await registerWebsocket(accessToken)
    const wsCallback = async (event) => {
      let wsData
      try {
        wsData = JSON.parse(event.data)
      } catch (error) {
        console.debug('json error', error)
        return
      }
      if (wsData.type === 'http.response.body') {
        let body
        try {
          body = atob(wsData.body).replace(/^data:/, '')
          const data = JSON.parse(body)
          console.debug('ws message', data)
          if (wsData.conversation_id === session.conversationId) {
            handleMessage(data)
          }
        } catch (error) {
          if (body && body.trim() === '[DONE]') {
            console.debug('ws message', '[DONE]')
            if (wsData.conversation_id === session.conversationId) {
              finishMessage()
              wsCallbacks = wsCallbacks.filter((cb) => cb !== wsCallback)
            }
          } else {
            console.debug('json error', error)
          }
        }
      }
    }
    wsCallbacks.push(wsCallback)
    const { conversationId, wsRequestId } = await sendWebsocketConversation(accessToken, options)
    session.conversationId = conversationId
    session.wsRequestId = wsRequestId
    port.postMessage({ session: session })
  } else {
    await fetchSSE(url, {
      ...options,
      onMessage(message) {
        //console.debug('sse message', message)
        if (message.trim() === '[DONE]') {
          finishMessage()
          return
        }
        let data
        try {
          data = JSON.parse(message)
        } catch (error) {
          console.debug('json error', error)
          return
        }
        handleMessage(data)
      },
      async onStart() {
        // sendModerations(accessToken, question, session.conversationId, session.messageId)
      },
      async onEnd() {
        port.postMessage({ done: true })
        cleanController()
      },
      async onError(resp) {
        cleanController()
        if (resp instanceof Error) throw resp
        if (resp.status === 403) {
          throw new Error('CLOUDFLARE')
        }
        const error = await resp.json().catch(() => ({}))
        throw new Error(
          !isEmpty(error) ? JSON.stringify(error) : `${resp.status} ${resp.statusText}`,
        )
      },
    })
  }

  function handleMessage(data) {
    if (data.error) {
      throw new Error(JSON.stringify(data.error))
    }

    if (data.conversation_id) session.conversationId = data.conversation_id
    if (data.message?.id) session.parentMessageId = data.message.id

    const respAns = data.message?.content?.parts?.[0]
    const contentType = data.message?.content?.content_type
    if (contentType === 'text' && respAns) {
      answer =
        generationPrefixAnswer +
        (generatedImageUrl && `\n\n![](${generatedImageUrl})\n\n`) +
        respAns
    } else if (contentType === 'code' && data.message?.status === 'in_progress') {
      const generationText = '\n\n' + t('Generating...')
      if (answer && !answer.endsWith(generationText)) generationPrefixAnswer = answer
      answer = generationPrefixAnswer + generationText
    } else if (
      contentType === 'multimodal_text' &&
      respAns?.content_type === 'image_asset_pointer'
    ) {
      const imageAsset = respAns?.asset_pointer || ''
      if (imageAsset) {
        fetch(
          `${config.customChatGptWebApiUrl}/backend-api/files/${imageAsset.replace(
            'file-service://',
            '',
          )}/download`,
          {
            credentials: 'include',
            headers: {
              Authorization: `Bearer ${accessToken}`,
              ...(cookie && { Cookie: cookie }),
            },
          },
        ).then((r) => r.json().then((json) => (generatedImageUrl = json?.download_url)))
      }
    }

    if (answer) {
      port.postMessage({ answer: answer, done: false, session: null })
    }
  }

  function finishMessage() {
    pushRecord(session, question, answer)
    console.debug('conversation history', { content: session.conversationRecords })
    port.postMessage({ answer: answer, done: true, session: session })
  }
}