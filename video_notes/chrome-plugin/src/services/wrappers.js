// 100% 复制自 chatGPTBox/src/services/wrappers.mjs
import {
  clearOldAccessToken,
  getUserConfig,
  isUsingChatgptWebModel,
  setAccessToken,
} from '../config/index.js'
import { modelNameToDesc, apiModeToModelName } from '../utils/model-name-convert.js'

// 模拟Browser API
const Browser = {
  cookies: {
    getAll: (details) => new Promise((resolve) => {
      chrome.cookies.getAll(details, resolve)
    }),
    get: (details) => new Promise((resolve) => {
      chrome.cookies.get(details, resolve)
    })
  }
}

// 模拟翻译函数
const t = (s) => s

export async function getChatGptAccessToken() {
  await clearOldAccessToken()
  const userConfig = await getUserConfig()
  if (userConfig.accessToken) {
    return userConfig.accessToken
  } else {
    const cookie = (await Browser.cookies.getAll({ url: 'https://chatgpt.com/' }))
      .map((cookie) => {
        return `${cookie.name}=${cookie.value}`
      })
      .join('; ')
    const resp = await fetch('https://chatgpt.com/api/auth/session', {
      headers: {
        Cookie: cookie,
      },
    })
    if (resp.status === 403) {
      throw new Error('CLOUDFLARE')
    }
    const data = await resp.json().catch(() => ({}))
    if (!data.accessToken) {
      throw new Error('UNAUTHORIZED')
    }
    await setAccessToken(data.accessToken)
    return data.accessToken
  }
}

export function handlePortError(session, port, err) {
  console.error(err)
  if (err.message) {
    if (!err.message.includes('aborted')) {
      if (
        ['message you submitted was too long', 'maximum context length'].some((m) =>
          err.message.includes(m),
        )
      )
        port.postMessage({ error: t('Exceeded maximum context length') + '\n\n' + err.message })
      else if (['CaptchaChallenge', 'CAPTCHA'].some((m) => err.message.includes(m)))
        port.postMessage({ error: t('Bing CaptchaChallenge') + '\n\n' + err.message })
      else if (['exceeded your current quota'].some((m) => err.message.includes(m)))
        port.postMessage({ error: t('Exceeded quota') + '\n\n' + err.message })
      else if (['Rate limit reached'].some((m) => err.message.includes(m)))
        port.postMessage({ error: t('Rate limit') + '\n\n' + err.message })
      else if (['authentication token has expired'].some((m) => err.message.includes(m)))
        port.postMessage({ error: 'UNAUTHORIZED' })
      else port.postMessage({ error: err.message })
    }
  } else {
    const errMsg = JSON.stringify(err)
    port.postMessage({ error: errMsg ?? 'unknown error' })
  }
}

export function registerPortListener(executor) {
  const Browser = {
    runtime: {
      onConnect: {
        addListener: (callback) => {
          chrome.runtime.onConnect.addListener(callback)
        }
      }
    }
  }
  
  Browser.runtime.onConnect.addListener((port) => {
    console.debug('Connected')
    const onMessage = async (msg) => {
      console.debug('Received message', msg)
      const session = msg.session
      if (!session) return
      const config = await getUserConfig()
      if (!session.modelName) session.modelName = config.modelName
      if (!session.apiMode && session.modelName !== 'customModel') session.apiMode = config.apiMode
      if (!session.aiName)
        session.aiName = modelNameToDesc(
          session.apiMode ? apiModeToModelName(session.apiMode) : session.modelName,
          t,
          config.customModelName,
        )
      port.postMessage({ session })
      try {
        await executor(session, port, config)
      } catch (err) {
        handlePortError(session, port, err)
      }
    }

    const onDisconnect = () => {
      console.debug('Port disconnected, removing listener')
      port.onMessage.removeListener(onMessage)
      port.onDisconnect.removeListener(onDisconnect)
    }

    port.onMessage.addListener(onMessage)
    port.onDisconnect.addListener(onDisconnect)
  })
}
