/**
 * Content Script - Main entry point for the extension.
 * This script acts as a simple loader. It determines which platform-specific
 * integration to load and then hands off control.
 */

console.log('[Content Script] Script loaded');

// Set the public path for webpack to ensure chunks are loaded from the extension's root.
__webpack_public_path__ = chrome.runtime.getURL('');

import { detectPlatform, PLATFORMS } from './utils/platform-utils.js';
import { YouTubeIntegration } from './sites/youtube-page.js';

// --- Configuration ---

const INTEGRATION_MAP = {
  [PLATFORMS.YOUTUBE]: (url) => YouTubeIntegration.create(url),
  // Future integrations can be added here, e.g.:
  // [PLATFORMS.VIMEO]: (url) => VimeoIntegration.create(url),
};

// --- State ---

// Keep a reference to the currently active integration module.
let activeIntegration = null;

// --- Core Logic ---

/**
 * Initializes the correct integration based on the current URL.
 * This function runs once on page load.
 */
async function initialize() {
  const url = window.location.href;
  console.log('[Content Script] Initializing for URL:', url);

  try {
    const platformName = detectPlatform(url);
    const createIntegration = INTEGRATION_MAP[platformName];

    if (createIntegration) {
      console.log(`[Content Script] ${platformName} platform detected. Loading integration...`);
      activeIntegration = await createIntegration(url);
    } else {
      console.log('[Content Script] No supported platform detected');
    }
  } catch (error) {
    console.error('[Content Script] Initialization failed:', error);
    if (activeIntegration && typeof activeIntegration.destroy === 'function') {
      activeIntegration.destroy();
    }
    activeIntegration = null;
  }
}

/**
 * Cleans up the active integration when the user navigates away from the page
 * or closes the tab.
 */
function handleUnload() {
  if (activeIntegration && typeof activeIntegration.destroy === 'function') {
    console.log('[Content Script] Page unloading. Destroying active integration');
    activeIntegration.destroy();
    activeIntegration = null;
  }
}

// --- Main Execution ---

// Run the initializer once the DOM is ready.
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  console.log('[Content Script] Document ready, calling initialize immediately');
  initialize();
} else {
  console.log('[Content Script] Document not ready, adding DOMContentLoaded listener');
  document.addEventListener('DOMContentLoaded', initialize, { once: true });
}

// Set up a listener to clean up when the user leaves the page entirely.
window.addEventListener('beforeunload', handleUnload);