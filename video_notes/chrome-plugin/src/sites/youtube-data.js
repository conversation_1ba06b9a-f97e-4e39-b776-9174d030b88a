/**
 * YouTube 字幕与 meta 信息获取适配器
 * This module provides a pure data-fetching service for YouTube captions and metadata.
 * It uses an async generator to yield progress states back to the caller.
 */

import { headAndTail } from '../utils/printLongText.js';
import { PANEL_STATES } from '../components/state_mgmt.js';

// --- Public API ---

/**
 * Fetches captions and video metadata for a given YouTube video ID.
 * This is an async generator that yields status updates and returns the final data.
 * 
 * @param {string} videoId - The ID of the YouTube video.
 * @yields {{status: string}} - An object indicating the current fetching stage.
 * @returns {Promise<{captions: Array, metaInfo: object}>} - A promise that resolves with the final captions and video metadata.
 * @throws {Error} If fetching or parsing fails at any stage.
 */
export async function* fetchCaptionsAndMeta(videoId) {
  try {
    yield { status: PANEL_STATES.FETCHING_VIDEO_INFO };
    const html = document.documentElement.outerHTML;
    const apiKey = _extractInnertubeApiKey(html, videoId);
    const apiUrl = `https://www.youtube.com/youtubei/v1/player?key=${apiKey}`;
    const requestBody = {
      context: { client: { clientName: "ANDROID", clientVersion: "20.10.38" } },
      videoId: videoId
    };
    const requestHeaders = { 'Content-Type': 'application/json' };

    yield { status: PANEL_STATES.FINDING_CAPTIONS };
    const resp = await fetch(apiUrl, {
      method: 'POST',
      headers: requestHeaders,
      body: JSON.stringify(requestBody),
      credentials: 'include'
    });
    if (!resp.ok) throw new Error(`YouTube API request failed with status ${resp.status}`);
    const data = await resp.json();
    const captionsJson = _extractCaptionsJson(data);
    const rawVideoDetails = data.videoDetails || {};
    const microformat = data.microformat?.playerMicroformatRenderer || {};

    // Transform videoDetails to metaInfo format
    const metaInfo = {
      author: rawVideoDetails.author || microformat.ownerChannelName || '',
      title: rawVideoDetails.title || '',
      publishDate: microformat.publishDate || microformat.uploadDate || ''
    };

    // If publishDate is empty, try to extract from page
    if (!metaInfo.publishDate) {
      console.log('[youtube-data.js] API returned empty publishDate, attempting page extraction');
      const pagePublishDate = _extractPublishDateFromPage();
      if (pagePublishDate) {
        metaInfo.publishDate = pagePublishDate;
        console.log('[youtube-data.js] Successfully extracted publishDate from page:', pagePublishDate);
      } else {
        console.warn('[youtube-data.js] Failed to extract publishDate from page');
      }
    }

    console.log('[youtube-data.js] author:', metaInfo.author, ', title:', metaInfo.title, ', publishDate:', metaInfo.publishDate)

    if (!captionsJson?.captionTracks?.length) {
      console.debug('[youtube-data.js] No caption tracks found, returning metadata.');
      return { captions: [], metaInfo };
    }

    yield { status: PANEL_STATES.DOWNLOADING_CAPTIONS };
    const track = captionsJson.captionTracks[0]; // Assuming we take the first available track
    const response = await fetch(track.baseUrl);
    if (!response.ok) {
      console.warn('[youtube-data.js] captions download failed, returning metadata');
      return { captions: [], metaInfo };
    }

    const subtitleContent = await response.text();
    yield { status: PANEL_STATES.PARSING_CAPTIONS };
    const captions = _parseTranscript(subtitleContent);

    if (captions && captions.length > 0) {
      console.log('[youtube-data.js] found', captions.length, 'captions')
    } else {
      console.warn('[youtube-data.js] Captions could not be parsed');
    }
    return { captions, metaInfo };
  } catch (error) {
    // Re-throw the error to be handled by the caller.
    console.error('[youtube-data.js] An error occurred:', error);
    throw error;
  }
}

// --- Internal Implementation ---

/**
 * Extract publishDate from page ytInitialPlayerResponse (fallback when API data is missing)
 * @returns {string} publishDate or empty string
 */
function _extractPublishDateFromPage() {
  try {
    let data = null;
    
    // Method 1: Try global variable (safer and preferred)
    if (typeof window !== 'undefined' && window.ytInitialPlayerResponse) {
      data = window.ytInitialPlayerResponse;
      console.debug('[youtube-data.js] Using window.ytInitialPlayerResponse');
    } else if (typeof ytInitialPlayerResponse !== 'undefined') {
      data = ytInitialPlayerResponse;
      console.debug('[youtube-data.js] Using global ytInitialPlayerResponse');
    } else {
      // Method 2: Extract from HTML (fallback, may trigger CSP warnings)
      console.debug('[youtube-data.js] Global variable not found, attempting HTML extraction');
      const html = document.documentElement.outerHTML;
      const patterns = [
        /var ytInitialPlayerResponse\s*=\s*({.+?});/s,
        /window\["ytInitialPlayerResponse"\]\s*=\s*({.+?});/s
      ];
      
      for (let i = 0; i < patterns.length; i++) {
        const match = html.match(patterns[i]);
        if (match) {
          try {
            // Use safer JSON parsing with additional validation
            const jsonString = match[1];
            if (jsonString && jsonString.startsWith('{') && jsonString.endsWith('}') && jsonString.length < 10000000) {
              data = JSON.parse(jsonString);
              console.debug(`[youtube-data.js] Extracted from HTML using pattern ${i + 1}`);
              break;
            } else {
              console.debug(`[youtube-data.js] Invalid JSON string format or too large`);
            }
          } catch (parseError) {
            console.debug(`[youtube-data.js] HTML pattern ${i + 1} parse failed:`, parseError.message);
            continue;
          }
        }
      }
    }
    
    if (!data) {
      console.debug('[youtube-data.js] No ytInitialPlayerResponse found in page');
      return '';
    }
    
    // Validate data structure
    if (typeof data !== 'object' || data === null) {
      console.debug('[youtube-data.js] Invalid data structure');
      return '';
    }
    
    // Extract publishDate from microformat
    const microformat = data.microformat?.playerMicroformatRenderer;
    if (microformat) {
      const publishDate = microformat.publishDate || microformat.uploadDate || '';
      console.debug('[youtube-data.js] Page microformat publishDate:', publishDate);
      return publishDate;
    } else {
      console.debug('[youtube-data.js] No microformat found in page data');
      return '';
    }
    
  } catch (error) {
    console.warn('[youtube-data.js] Page publishDate extraction failed:', error.message);
    return '';
  }
}

function _extractInnertubeApiKey(html, videoId) {
  const pattern = /"INNERTUBE_API_KEY":\s*"([a-zA-Z0-9_-]+)"/;
  const match = html.match(pattern);
  if (match && match[1]) return match[1];
  throw new Error(`Could not extract INNERTUBE_API_KEY for video ID: ${videoId}`);
}

function _extractCaptionsJson(innertubeData) {
  return innertubeData.captions?.playerCaptionsTracklistRenderer || {};
}

function _parseTranscript(rawData) {
  const trimmedData = rawData.trim();
  if (!trimmedData) {
    return [];
  }

  for (const parser of PARSERS) {
    if (parser.canParse(trimmedData)) {
      try {
        const subtitles = parser.run(trimmedData);
        if (subtitles && subtitles.length > 0) {
          console.log(`[youtube-data.js] Successfully parsed subtitles using '${parser.name}' parser.`);
          return subtitles;
        }
      } catch (e) {
        console.debug(`[youtube-data.js] Parser '${parser.name}' failed:`, e.message);
      }
    }
  }
  console.error('[youtube-data.js] Failed to parse subtitles. No suitable parser found.');
  headAndTail(rawData, 1000, 150, 'rawData');
  return [];
}

const PARSERS = [
  {
    name: 'JSON (srv3)',
    canParse: data => data.trim().startsWith('{'),
    run: _parseJsonTranscript
  },
  {
    name: 'XML (p-tags)',
    canParse: data => data.includes('</p>'),
    run: _parseXmlTranscript
  },
  {
    name: 'Legacy XML (text-tags)',
    canParse: data => data.includes('</text>'),
    run: _parseLegacyXmlTranscript
  }
];

function _parseJsonTranscript(jsonString) {
  const json = JSON.parse(jsonString);
  if (!json?.events?.length) return [];

  const subtitles = [];
  for (const e of json.events) {
    if (!e.segs) continue;
    const text = e.segs.map(s => s.utf8).join('');
    if (!text.trim()) continue;
    const start = (e.tStartMs || 0) / 1000;
    const duration = (e.dDurationMs || 0) / 1000;
    const end = start + duration;
    subtitles.push({ text: _decodeHtmlEntities(text), start, end });
  }
  return subtitles;
}

function _parseXmlTranscript(xmlString) {
  const parser = new DOMParser();
  const doc = parser.parseFromString(xmlString, 'text/xml');
  const pElements = doc.querySelectorAll('p');
  if (pElements.length === 0) return null;

  const subtitles = [];
  pElements.forEach(p => {
    const start = parseFloat(p.getAttribute('t')) / 1000 || 0;
    const duration = parseFloat(p.getAttribute('d')) / 1000 || 0;
    const end = start + duration;
    let text = '';
    const sElements = p.querySelectorAll('s');
    if (sElements.length > 0) {
      sElements.forEach(s => { text += (s.textContent || ''); });
    } else {
      text = p.textContent || '';
    }
    if (text.trim()) subtitles.push({ text: _decodeHtmlEntities(text), start, end });
  });
  return subtitles;
}

function _parseLegacyXmlTranscript(xmlString) {
  const parser = new DOMParser();
  const doc = parser.parseFromString(xmlString, 'text/xml');
  const textElements = doc.querySelectorAll('text');
  if (textElements.length === 0) return null;

  const subtitles = [];
  for (const element of textElements) {
    const text = element.textContent;
    if (text) {
      const start = parseFloat(element.getAttribute('start')) || 0;
      const duration = parseFloat(element.getAttribute('dur')) || 0;
      const end = start + duration;
      subtitles.push({ text: _decodeHtmlEntities(text), start, end });
    }
  }
  return subtitles;
}


function _decodeHtmlEntities(text) {
  // A more robust, non-DOM-based HTML entity decoder.
  return text.replace(/&#(\d+);/g, (match, dec) => String.fromCharCode(dec))
    .replace(/&quot;/g, '"')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&#39;/g, "'");
}