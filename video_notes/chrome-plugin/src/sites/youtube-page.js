/**
 * YouTube Integration Module
 * A self-contained class that manages the SiderPanel lifecycle on the YouTube platform.
 * It listens for YouTube-specific navigation events and updates the panel accordingly.
 */

import { SiderPanel } from '../components/sider-panel.js';
import { fetchCaptionsAndMeta } from './youtube-data.js';
import { getVideoId } from '../utils/platform-utils.js';

const CONSTANTS = {
  INSERTION_POINT_SELECTOR: '#secondary',
  INSERTION_WAIT_TIMEOUT: 5000,
  NAVIGATION_DEBOUNCE_MS: 100,
};

const PRIVATE_KEY = Symbol('private');

class YouTubeIntegration {
  // --- Class Fields ---
  #panel = null;
  #currentVideoId = null;
  #isInitialized = false;
  #navigationTimeoutId = null;
  #boundHandleNavigation = null;

  // --- Static Methods ---
  /**
   * Creates and initializes the YouTube integration.
   * @param {string} initialUrl - The URL of the page when the integration is created.
   */
  static async create(initialUrl) {
    const instance = new YouTubeIntegration(PRIVATE_KEY);
    await instance.#initialize(initialUrl);
    return instance;
  }

  // --- Constructor ---
  constructor(key) {
    if (key !== PRIVATE_KEY) {
      throw new Error('YouTubeIntegration constructor is private. Use YouTubeIntegration.create() instead.');
    }
    this.#boundHandleNavigation = this.#handleNavigation.bind(this);
  }

  // --- Public Methods ---
  /**
   * Destroys the integration and cleans up all resources.
   */
  destroy() {
    console.log('[YouTubeIntegration] Destroying integration');
    this.#cleanupEventListeners();

    if (this.#panel) {
      this.#panel.destroy();
    }

    this.#panel = null;
    this.#currentVideoId = null;
    this.#isInitialized = false;
    if (this.#navigationTimeoutId) {
      clearTimeout(this.#navigationTimeoutId);
      this.#navigationTimeoutId = null;
    }
  }

  // --- Private Methods ---
  async #initialize(initialUrl) {
    if (this.#isInitialized) {
      console.log('[YouTubeIntegration] Integration already initialized');
      return;
    }
    console.log('[YouTubeIntegration] Creating SiderPanel...');

    try {
      const insertionPoint = await this.#findInsertionPoint();
      const mode = insertionPoint ? 'embedded' : 'floating';
      const container = insertionPoint || document.body;

      this.#panel = await SiderPanel.create(container, mode, fetchCaptionsAndMeta);

      if (!this.#panel) {
        console.error('[YouTubeIntegration] SiderPanel creation failed');
        this.destroy();
        return;
      }

      const initialVideoId = getVideoId(initialUrl);
      if (initialVideoId) {
        await this.#panel.onLoad(initialVideoId);
        this.#currentVideoId = initialVideoId;
        this.#panel.show();
      } else {
        this.#panel.hide();
      }

      this.#setupEventListeners();
      this.#isInitialized = true;
      console.log(`[YouTubeIntegration] Integration created successfully. Initial video ID: ${this.#currentVideoId}`);

    } catch (error) {
      console.error('[YouTubeIntegration] Failed to create SiderPanel:', error);
      this.destroy();
    }
  }

  #setupEventListeners() {
    window.addEventListener('yt-navigate-finish', this.#boundHandleNavigation);
    window.addEventListener('popstate', this.#boundHandleNavigation);
  }

  #cleanupEventListeners() {
    window.removeEventListener('yt-navigate-finish', this.#boundHandleNavigation);
    window.removeEventListener('popstate', this.#boundHandleNavigation);
  }

  #handleNavigation() {
    clearTimeout(this.#navigationTimeoutId);
    this.#navigationTimeoutId = setTimeout(() => this.#processNavigation(), CONSTANTS.NAVIGATION_DEBOUNCE_MS);
  }

  async #processNavigation() {
    if (!this.#isInitialized || !this.#panel) {
      return;
    }
    const newVideoId = getVideoId(location.href);

    if (newVideoId && newVideoId !== this.#currentVideoId) {
      console.log(`[YouTubeIntegration] Navigated to new video: ${newVideoId}`);
      this.#currentVideoId = newVideoId;
      await this.#panel.onLoad(newVideoId);
      await this.#repositionEmbeddedPanelIfNeeded();
      this.#panel.show();
    } else if (!newVideoId && this.#currentVideoId) {
      console.log('[YouTubeIntegration] Navigated away from video page');
      this.#currentVideoId = null;
      this.#panel.hide();
    }
  }

  async #findInsertionPoint() {
    return this.#waitForElement(CONSTANTS.INSERTION_POINT_SELECTOR, CONSTANTS.INSERTION_WAIT_TIMEOUT);
  }

  async #repositionEmbeddedPanelIfNeeded() {
    if (!this.#panel) return;
    const panelElement = this.#panel.getPanelElement();
    if (!panelElement || panelElement.classList.contains('floating')) {
      return;
    }
    const newInsertionPoint = await this.#findInsertionPoint();
    if (newInsertionPoint && panelElement.parentElement !== newInsertionPoint) {
      console.log('[YouTubeIntegration] Repositioning embedded panel');
      newInsertionPoint.insertBefore(panelElement, newInsertionPoint.firstChild);
    }
  }

  #waitForElement(selector, timeout) {
    let isSettled = false;
    return new Promise(resolve => {
      const element = document.querySelector(selector);
      if (element) {
        isSettled = true;
        return resolve(element);
      }

      const observer = new MutationObserver(() => {
        if (isSettled) return;
        const el = document.querySelector(selector);
        if (el) {
          isSettled = true;
          observer.disconnect();
          resolve(el);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        if (isSettled) return;
        isSettled = true;
        observer.disconnect();
        console.warn(`[YouTubeIntegration] Timed out waiting for element: ${selector}`);
        resolve(null);
      }, timeout);
    });
  }
}

// The public interface is now the class itself.
export { YouTubeIntegration };