import { encode, decode } from '@nem035/gpt-3-encoder'
import { getUserConfig, getModelTokenLimit } from '../config/index.js'

/**
 * 简化的文本裁剪函数，专为视频字幕总结优化
 * @param {string} text - 原始文本
 * @param {number} reserveTokens - 为响应预留的 token 数量
 * @returns {Promise<string>} 裁剪后的文本
 */
export async function cropText(text, reserveTokens = 1000) {
  const userConfig = await getUserConfig()

  // 跳过裁剪检查
  if (!userConfig.cropText) return text

  try {
    // 获取模型最大 token 限制
    const maxTokens = getModelTokenLimit(userConfig)
    const targetLength = maxTokens - reserveTokens  // 预留响应空间

    // 计算原文 token 长度
    const originalTokens = encode(text)
    const originalLength = originalTokens.length

    // 如果不需要截断，直接返回
    if (originalLength <= targetLength) {
      //console.log(`Text length: ${originalLength} tokens (no truncation needed)`)
      return text
    }

    // 截取前面部分
    const truncatedTokens = originalTokens.slice(0, targetLength)
    const truncatedText = decode(truncatedTokens)

    // 计算截断比例
    const truncatedPercentage = ((originalLength - targetLength) / originalLength * 100).toFixed(1)

    console.log(
      `Text truncated: ${originalLength} → ${targetLength} tokens ` +
      `(${truncatedPercentage}% content removed)`
    )

    return truncatedText

  } catch (error) {
    console.error('Text cropping failed:', error)
    // 降级处理：简单字符截断
    const fallbackLength = Math.floor(text.length * 0.7)
    console.log(`Fallback: truncated to ${fallbackLength} characters`)
    return text.substring(0, fallbackLength)
  }
}
