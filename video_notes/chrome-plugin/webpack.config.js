const path = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  entry: {
    'content-script': './src/content-script/index.js',
    'background': './src/background/index.js'
  },
  output: {
    path: path.resolve(__dirname, 'build'),
    filename: '[name].js',
    chunkFilename: '[name].js',
    publicPath: '',
    clean: true
  },
  target: 'web',
  // 启用源码映射，便于调试
  devtool: process.env.NODE_ENV === 'production' ? false : 'source-map',
  // 优化配置
  optimization: {
    // 禁用树摇，确保所有导出的类都被保留
    usedExports: false,
    // 禁用模块连接，避免代码被过度优化
    concatenateModules: false,
    // 启用代码压缩
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          format: {
            comments: false,
          },
          compress: {
            drop_console: process.env.NODE_ENV === 'production',
            drop_debugger: process.env.NODE_ENV === 'production',
          },
          // 保留类名
          keep_classnames: true,
          // 保留函数名
          keep_fnames: true,
        },
        extractComments: false,
      }),
    ],
    // 禁用代码分割，将所有代码打包到一个文件中
    splitChunks: false,
  },
  // 解析配置
  resolve: {
    // 设置模块别名
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
    // 自动解析扩展名
    extensions: ['.js', '.json'],
    // 设置模块查找目录
    modules: ['node_modules', path.resolve(__dirname, 'src')],
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', {
                // 设置目标浏览器
                targets: {
                  chrome: '80',
                },
                // 启用按需加载 polyfill
                useBuiltIns: 'usage',
                corejs: 3,
                // 启用松散模式，提高兼容性
                loose: true,
                // 启用模块处理
                modules: false,
              }]
            ],
            // 启用缓存，提高构建速度
            cacheDirectory: true,
          }
        }
      }
    ]
  },
  plugins: [
    new CopyWebpackPlugin({
      patterns: [
        { from: 'src/manifest.json', to: 'manifest.json' },
        { from: 'src/rules.json', to: 'rules.json' },
        { from: 'src/config/prompt.md', to: 'prompt.md' },
        { from: 'src/components/sider-panel.css', to: 'sider-panel.css' }
      ]
    })
  ]
};