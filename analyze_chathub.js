// 分析 ChatHub 扩展的脚本
// 在 Chrome DevTools Console 中运行

console.log("=== ChatHub Extension Analysis ===");

// 1. 检查扩展的基本信息
console.log("Extension ID:", chrome.runtime.id);

// 2. 检查 declarativeNetRequest 规则
chrome.declarativeNetRequest.getDynamicRules().then(rules => {
  console.log("Dynamic Rules:", rules);
});

chrome.declarativeNetRequest.getSessionRules().then(rules => {
  console.log("Session Rules:", rules);
});

// 3. 监听网络请求
chrome.webRequest.onBeforeRequest.addListener(
  function(details) {
    if (details.url.includes('chatgpt.com') || details.url.includes('openai.com')) {
      console.log("ChatGPT Request:", details);
    }
  },
  {urls: ["<all_urls>"]},
  ["requestBody"]
);

// 4. 监听请求头修改
chrome.webRequest.onBeforeSendHeaders.addListener(
  function(details) {
    if (details.url.includes('chatgpt.com') || details.url.includes('openai.com')) {
      console.log("ChatGPT Headers:", details.requestHeaders);
    }
  },
  {urls: ["<all_urls>"]},
  ["requestHeaders"]
);

// 5. 检查存储的数据
chrome.storage.local.get(null).then(data => {
  console.log("Local Storage:", Object.keys(data));
});

chrome.storage.session.get(null).then(data => {
  console.log("Session Storage:", Object.keys(data));
});

// 6. 检查活动标签页
chrome.tabs.query({}, tabs => {
  const chatgptTabs = tabs.filter(tab => 
    tab.url && (tab.url.includes('chatgpt.com') || tab.url.includes('openai.com'))
  );
  console.log("ChatGPT Tabs:", chatgptTabs.map(tab => ({
    id: tab.id,
    url: tab.url,
    pinned: tab.pinned
  })));
});

console.log("=== Analysis Complete ===");
